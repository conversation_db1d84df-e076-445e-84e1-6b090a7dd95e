# Cerberus - 分布式智能安防监控平台

**项目代号:** Cerberus (地狱犬，寓意多头守护)  
**版本:** 2.0  
**架构:** 边缘-中心（Edge-Central）分布式架构

## 系统概述

Cerberus是一个专为大规模部署而设计的分布式智能安防监控平台。它采用边缘-中心架构，将实时视频分析任务分配给部署在现场的边缘处理节点，由中央管理平台进行统一的数据汇聚、存储、分析和可视化管理。

## 系统架构

```
[摄像头] -> [Edge Node] -> [Message Queue & Distributed Storage] -> [Central Platform] -> [Web管理界面]
```

### 核心组件

1. **边缘处理节点 (Edge Node)** - Python/Flask
   - 实时视频流处理和AI分析
   - 事件触发式录像
   - 本地缓存和离线容错

2. **中央管理平台 (Central Platform)** - FastAPI
   - 数据聚合和存储
   - Web管理界面
   - 系统监控和配置管理

### 支撑基础设施

- **消息队列:** MQTT Broker (EMQ X)
- **分布式存储:** MinIO (S3兼容)
- **中央数据库:** PostgreSQL

## 项目结构

```
cerberus/
├── edge-node/              # 边缘处理节点
│   ├── src/
│   ├── models/             # AI模型文件
│   ├── config/
│   ├── tests/
│   └── Dockerfile
├── central-platform/       # 中央管理平台
│   ├── backend/            # FastAPI后端
│   ├── frontend/           # Web前端
│   ├── config/
│   ├── tests/
│   └── Dockerfile
├── infrastructure/         # 基础设施配置
│   ├── mqtt/
│   ├── database/
│   ├── storage/
│   └── monitoring/
├── shared/                 # 共享代码和配置
│   ├── models/             # 数据模型
│   ├── protocols/          # 通信协议
│   └── utils/
├── docs/                   # 文档
├── scripts/                # 部署和运维脚本
├── docker-compose.yml      # 开发环境
├── docker-compose.prod.yml # 生产环境
└── README.md
```

## 快速开始

### 开发环境启动

```bash
# 启动基础设施服务
docker-compose up -d

# 启动边缘节点
cd edge-node
python -m pip install -r requirements.txt
python app.py

# 启动中央平台
cd central-platform/backend
python -m pip install -r requirements.txt
uvicorn main:app --reload
```

### 生产环境部署

```bash
# 使用生产配置启动
docker-compose -f docker-compose.prod.yml up -d
```

## 技术栈

### 边缘节点
- **语言:** Python 3.9+
- **框架:** Flask
- **AI框架:** ONNX Runtime
- **视频处理:** OpenCV
- **消息队列:** Paho-MQTT
- **本地存储:** SQLite

### 中央平台
- **后端:** FastAPI + SQLAlchemy
- **前端:** Bootstrap 5 + Vanilla JavaScript
- **数据库:** PostgreSQL
- **Web服务器:** Uvicorn + Gunicorn

### 基础设施
- **消息队列:** EMQ X MQTT Broker
- **对象存储:** MinIO
- **容器化:** Docker + Docker Compose
- **编排:** Kubernetes (生产环境)

## 开发指南

详细的开发指南请参考 [docs/development.md](docs/development.md)

## 部署指南

详细的部署指南请参考 [docs/deployment.md](docs/deployment.md)

## API文档

API文档可在中央平台启动后访问：http://localhost:8000/docs

## 许可证

[MIT License](LICENSE)
